🎯 WINDOWS SERVER 2019 REDIS KURULUM REHBERİ
================================================================
PROJE: GymKod Pro - Production Redis Deployment
SUNUCU: Windows Server 2019 (4GB RAM, 2CPU)
MEVCUT DURUM: 1 salon, 100 üye
HEDEF: Ölçeklenebilir Redis altyapısı

================================================================
SEÇENEK 1: DOCKER ENGINE KURULUMU (ÖNERİLEN)
================================================================

ADIM 1: DOCKER ENGINE KURULUMU
------------------------------
1. Windows Server 2019'da PowerShell'i Administrator olarak aç

2. Windows Container feature'ını etkinleştir:
   Enable-WindowsOptionalFeature -Online -FeatureName containers -All
   
3. Hyper-V feature'ını etkinleştir (gerekirse):
   Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

4. Sunucuyu yeniden başlat:
   Restart-Computer

5. Docker Engine'i indir ve kur:
   # Docker Engine'i indir
   Invoke-WebRequest -UseBasicParsing "https://download.docker.com/win/static/stable/x86_64/docker-24.0.7.zip" -o "docker.zip"
   
   # Zip'i aç
   Expand-Archive docker.zip -DestinationPath $Env:ProgramFiles -Force
   
   # PATH'e ekle
   $env:path += ";$env:ProgramFiles\docker"
   [Environment]::SetEnvironmentVariable("Path", $env:path, [EnvironmentVariableTarget]::Machine)

6. Docker daemon'u service olarak kur:
   dockerd --register-service
   Start-Service docker

7. Docker kurulumunu test et:
   docker version
   docker run hello-world

ADIM 2: PRODUCTION DOCKER-COMPOSE HAZIRLA
-----------------------------------------
1. Sunucuda C:\GymKod klasörü oluştur:
   mkdir C:\GymKod
   cd C:\GymKod

2. docker-compose.prod.yml dosyası oluştur:

version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.prod.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymKod2024Redis!
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "GymKod2024Redis!", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge

ADIM 3: PRODUCTION REDIS CONFIGURATION
--------------------------------------
1. redis.prod.conf dosyası oluştur:

# Redis Production Configuration for GymKod
# Bind to all interfaces
bind 0.0.0.0

# Port
port 6379

# Password protection
requirepass GymKod2024Redis!

# Memory management (2GB for production)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence - Production settings
save 900 1
save 300 10
save 60 10000

# AOF persistence
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile /data/redis.log

# Performance tuning
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Security
protected-mode yes

# Database count
databases 16

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

ADIM 4: REDIS DEPLOYMENT
------------------------
1. Redis container'ı başlat:
   cd C:\GymKod
   docker-compose -f docker-compose.prod.yml up -d

2. Container durumunu kontrol et:
   docker ps
   docker logs gymkod-redis-prod

3. Redis bağlantısını test et:
   docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

4. Memory kullanımını kontrol et:
   docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory

================================================================
SEÇENEK 2: NATIVE REDIS KURULUMU (ALTERNATIF)
================================================================

ADIM 1: REDIS WINDOWS BINARY KURULUMU
-------------------------------------
1. Redis Windows binary'sini indir:
   # PowerShell'de
   Invoke-WebRequest -Uri "https://github.com/tporadowski/redis/releases/download/v********/Redis-x64-********.zip" -OutFile "redis.zip"

2. Redis'i C:\Redis klasörüne çıkar:
   Expand-Archive -Path "redis.zip" -DestinationPath "C:\Redis"

3. Redis'i Windows Service olarak kur:
   cd C:\Redis
   redis-server --service-install redis.windows-service.conf --loglevel verbose

4. Redis service'ini başlat:
   redis-server --service-start

ADIM 2: REDIS CONFIGURATION (NATIVE)
------------------------------------
1. C:\Redis\redis.windows-service.conf dosyasını düzenle:

# Windows Redis Production Configuration
port 6379
bind 127.0.0.1
requirepass GymKod2024Redis!
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
logfile C:\Redis\logs\redis.log
loglevel notice

================================================================
ADIM 5: FIREWALL VE GÜVENLİK AYARLARI
================================================================

1. Windows Firewall'da Redis portunu aç:
   New-NetFirewallRule -DisplayName "Redis" -Direction Inbound -Protocol TCP -LocalPort 6379

2. Sadece local bağlantılara izin ver (güvenlik için):
   # redis.conf'da bind 127.0.0.1 olarak ayarla

3. Strong password kullan (zaten mevcut):
   requirepass GymKod2024Redis!

================================================================
ADIM 6: MONITORING VE BACKUP SETUP
================================================================

1. Redis monitoring script'i oluştur (C:\GymKod\monitor.ps1):

# Redis Health Check Script
$redisHost = "localhost"
$redisPort = 6379
$redisPassword = "GymKod2024Redis!"

try {
    $result = docker exec gymkod-redis-prod redis-cli -a $redisPassword ping
    if ($result -eq "PONG") {
        Write-Host "Redis is healthy" -ForegroundColor Green
        # Log to file
        Add-Content -Path "C:\GymKod\logs\redis-health.log" -Value "$(Get-Date): Redis is healthy"
    }
} catch {
    Write-Host "Redis is down!" -ForegroundColor Red
    Add-Content -Path "C:\GymKod\logs\redis-health.log" -Value "$(Get-Date): Redis is down - $($_.Exception.Message)"
    # Restart Redis if needed
    docker restart gymkod-redis-prod
}

2. Backup script'i oluştur (C:\GymKod\backup.ps1):

# Redis Backup Script
$backupPath = "C:\GymKod\backups"
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# Create backup directory
if (!(Test-Path $backupPath)) {
    New-Item -ItemType Directory -Path $backupPath
}

# Create Redis backup
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! BGSAVE
Start-Sleep -Seconds 10

# Copy dump.rdb file
docker cp gymkod-redis-prod:/data/dump.rdb "$backupPath\redis_backup_$timestamp.rdb"

Write-Host "Backup created: redis_backup_$timestamp.rdb"

3. Task Scheduler ile otomatik çalıştır:
   # Monitoring: Her 5 dakikada bir
   schtasks /create /tn "Redis Health Check" /tr "powershell.exe -File C:\GymKod\monitor.ps1" /sc minute /mo 5
   
   # Backup: Günde bir kez (gece 2:00)
   schtasks /create /tn "Redis Backup" /tr "powershell.exe -File C:\GymKod\backup.ps1" /sc daily /st 02:00

================================================================
ADIM 7: .NET APPLICATION CONFIGURATION
================================================================

1. appsettings.json'da production Redis connection string'i güncelle:

"Redis": {
  "dev": "localhost:6379,password=GymKod2024Redis!,abortConnect=false",
  "staging": "localhost:6379,password=GymKod2024Redis!,abortConnect=false",
  "canlı": "SUNUCU_IP:6379,password=GymKod2024Redis!,abortConnect=false,connectTimeout=5000,syncTimeout=5000"
}

2. Production deployment için connection string'i environment variable olarak ayarla:
   setx REDIS_CONNECTION_STRING "localhost:6379,password=GymKod2024Redis!,abortConnect=false"

================================================================
ADIM 8: PERFORMANCE TESTING VE OPTIMIZATION
================================================================

1. Redis performance test:
   docker exec -it gymkod-redis-prod redis-benchmark -a GymKod2024Redis! -n 10000 -c 50

2. Memory usage monitoring:
   docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory

3. Cache hit ratio monitoring:
   docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! info stats

================================================================
TROUBLESHOOTING GUIDE
================================================================

PROBLEM 1: Docker kurulumu başarısız
ÇÖZÜM: Windows Container feature'ını kontrol et, sunucuyu yeniden başlat

PROBLEM 2: Redis container başlamıyor
ÇÖZÜM: 
- docker logs gymkod-redis-prod
- Port 6379'un kullanımda olup olmadığını kontrol et: netstat -an | findstr 6379

PROBLEM 3: .NET uygulaması Redis'e bağlanamıyor
ÇÖZÜM:
- Redis container'ının çalıştığını kontrol et
- Firewall ayarlarını kontrol et
- Connection string'i doğrula

PROBLEM 4: Memory usage yüksek
ÇÖZÜM:
- maxmemory ayarını kontrol et
- Cache TTL değerlerini gözden geçir
- Gereksiz cache'leri temizle

================================================================
NEXT STEPS (KURULUM SONRASI)
================================================================

1. ✅ Redis kurulumu tamamlandı
2. ✅ Monitoring ve backup aktif
3. ⏳ .NET uygulamasını production Redis'e bağla
4. ⏳ Performance test yap
5. ⏳ Cache hit ratio'yu optimize et
6. ⏳ Scaling için hazırlık yap (Redis Cluster)

================================================================
CONTACT & SUPPORT
================================================================

Kurulum sırasında sorun yaşarsan:
1. Docker logs'ları kontrol et
2. Windows Event Viewer'ı incele
3. Redis log dosyalarını kontrol et
4. Performance counter'ları izle

Bu rehberi takip ederek Windows Server 2019'da production-ready Redis kurulumu yapabilirsin!

================================================================
ÖZEL DURUMUNUZ İÇİN EK BİLGİLER
================================================================

MEVCUT CACHE SİSTEMİNİZ HAKKINDA:
✅ Çok iyi kurulmuş - AOP aspects, multi-tenant key strategy
✅ Manager sınıflarında cache entegrasyonu mevcut
✅ Cache invalidation sistemi aktif
✅ Performance monitoring var

SİZİN İÇİN ÖNERİLER:

1. DOCKER DESKTOP YERİNE DOCKER ENGINE:
   - Docker Desktop Windows Server'da desteklenmiyor
   - Docker Engine (yukarıdaki rehber) kullanın
   - Daha hafif ve server ortamı için uygun

2. 4GB RAM İÇİN OPTIMIZATION:
   - Redis için 2GB ayırın (maxmemory 2gb)
   - Kalan 2GB OS ve .NET app için
   - Cache TTL değerlerini gözden geçirin

3. 1 SALON 100 ÜYE İÇİN:
   - Mevcut cache ayarlarınız yeterli
   - Hot data: 5dk (GetMemberDetails)
   - Warm data: 10-30dk (Pagination)
   - Cold data: 1-4sa (Master data)

4. BÜYÜME İÇİN HAZIRLIK:
   - Redis memory monitoring kurun
   - Cache hit ratio'yu takip edin
   - 1000+ salon için Redis Cluster planlayın

5. MEVCUT CACHE ENTEGRASYONUNUZ:
   - MemberManager: ✅ Cache var
   - PaymentManager: ✅ Cache var
   - MembershipManager: ✅ Cache var
   - Diğer manager'lar için cache ekleme planı yapın

================================================================
KURULUM SONRASI TEST PLANI
================================================================

1. REDIS BAĞLANTI TESTİ:
   docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

2. .NET UYGULAMANIZI TEST EDİN:
   - GetMemberDetails API'sini çağırın
   - İlk çağrı: DB'den (yavaş)
   - İkinci çağrı: Cache'den (hızlı)

3. CACHE KEY'LERİNİ KONTROL EDİN:
   docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! KEYS "gym:1:*"

4. MEMORY KULLANIMI:
   docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory

================================================================
SORUN ÇÖZÜM REHBERİ
================================================================

SORU: Docker Engine kurulumu başarısız olursa?
CEVAP:
1. Windows Container feature'ını kontrol edin
2. Sunucuyu yeniden başlatın
3. Alternatif olarak Native Redis kurulumu yapın

SORU: Redis'e bağlanamıyorsam?
CEVAP:
1. Container çalışıyor mu: docker ps
2. Port açık mı: netstat -an | findstr 6379
3. Password doğru mu: GymKod2024Redis!

SORU: Memory usage yüksekse?
CEVAP:
1. maxmemory 2gb ayarını kontrol edin
2. Cache TTL değerlerini azaltın
3. Gereksiz cache'leri temizleyin

SORU: Performance düşükse?
CEVAP:
1. Cache hit ratio'yu kontrol edin (>90% olmalı)
2. Network latency'yi test edin
3. Redis slow log'unu inceleyin

================================================================
BAŞLAMAYA HAZIR MISINIZ?
================================================================

1. Yukarıdaki rehberi adım adım takip edin
2. Her adımda test yapın
3. Sorun yaşarsanız troubleshooting guide'ı kullanın
4. Kurulum tamamlandıktan sonra performance test yapın

BAŞARILI KURULUM SONRASI:
- Redis production'da çalışacak
- Cache sisteminiz aktif olacak
- 100+ üye için optimize edilmiş olacak
- Büyüme için hazır altyapı kurulmuş olacak

Hangi adımdan başlamak istiyorsunuz? Docker Engine kurulumu mu yoksa başka bir sorunuz var mı?
