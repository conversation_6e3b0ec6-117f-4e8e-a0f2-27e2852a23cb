🐳 WINDOWS SERVER 2019 DOCKER ENGINE KURULUM REHBERİ
================================================================
PROJE: GymKod Pro - Production Docker Engine Kurulumu
SUNUCU: Windows Server 2019 (4GB RAM, 2 CPU)
HEDEF: Production-ready Docker Engine kurulumu

================================================================
NEDEN DOCKER ENGINE? (Docker Desktop değil)
================================================================

✅ DOCKER ENGINE AVANTAJLARI:
- Windows Server için optimize edilmiş
- Daha az resource kullanımı
- Production ortamı için tasarlanmış
- GUI gerektirmez (headless)
- Daha stabil ve güvenilir
- Lisans sorunu yok

❌ DOCKER DESKTOP DEZAVANTAJLARI:
- Windows Server'da desteklenmiyor
- GUI gerektirir
- Daha fazla resource kullanır
- Development ortamı için tasarlanmış

================================================================
AŞAMA 1: SİSTEM HAZIRLIK (15 dakika)
================================================================

1.1 WINDOWS UPDATE
------------------
• Windows Server 2019'u güncelleyin:
  - Settings > Update & Security > Windows Update
  - "Check for updates" tıklayın
  - Tüm kritik güncellemeleri yükleyin
  - Sunucuyu yeniden başlatın

1.2 POWERSHELL VERSİYON KONTROLÜ
--------------------------------
• PowerShell'i Administrator olarak açın
• PowerShell versiyonunu kontrol edin:
  ```powershell
  $PSVersionTable.PSVersion
  ```
• Eğer 5.1'den düşükse PowerShell 7 kurun:
  ```powershell
  # PowerShell 7 kurulumu
  iex "& { $(irm https://aka.ms/install-powershell.ps1) } -UseMSI"
  ```

1.3 EXECUTION POLICY AYARLAMA
-----------------------------
• PowerShell execution policy'yi ayarlayın:
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine
  ```

================================================================
AŞAMA 2: WINDOWS FEATURES ETKINLEŞTIRME (10 dakika)
================================================================

2.1 CONTAINERS FEATURE
-----------------------
• PowerShell'i Administrator olarak açın
• Containers feature'ını etkinleştirin:
  ```powershell
  Enable-WindowsOptionalFeature -Online -FeatureName containers -All
  ```

2.2 HYPER-V ETKİNLEŞTİRME (Opsiyonel)
-------------------------------------
• Hyper-V'yi etkinleştirin (Linux container'lar için):
  ```powershell
  Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
  ```

• Sunucuyu yeniden başlatın:
  ```powershell
  Restart-Computer -Force
  ```

================================================================
AŞAMA 3: DOCKER ENGINE KURULUMU (20 dakika)
================================================================

3.1 CHOCOLATEY KURULUMU
-----------------------
• PowerShell'i Administrator olarak açın
• Chocolatey package manager'ı kurun:
  ```powershell
  Set-ExecutionPolicy Bypass -Scope Process -Force
  [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
  iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
  ```

• Chocolatey kurulumunu doğrulayın:
  ```powershell
  choco --version
  ```

3.2 DOCKER ENGINE KURULUMU
---------------------------
• Docker Engine'i Chocolatey ile kurun:
  ```powershell
  choco install docker-engine -y
  ```

• Alternatif olarak manuel kurulum:
  ```powershell
  # Docker Engine'i manuel indirme
  Invoke-WebRequest -UseBasicParsing "https://download.docker.com/win/static/stable/x86_64/docker-20.10.21.zip" -o "$env:TEMP\docker.zip"
  
  # Zip dosyasını çıkarma
  Expand-Archive "$env:TEMP\docker.zip" -DestinationPath $env:ProgramFiles -Force
  
  # PATH'e ekleme
  $env:PATH += ";$env:ProgramFiles\docker"
  [Environment]::SetEnvironmentVariable("PATH", $env:PATH, [EnvironmentVariableTarget]::Machine)
  ```

3.3 DOCKER SERVICE KURULUMU
----------------------------
• Docker daemon'ı Windows Service olarak kaydedin:
  ```powershell
  dockerd --register-service
  ```

• Docker service'ini başlatın:
  ```powershell
  Start-Service docker
  ```

• Service'in otomatik başlamasını sağlayın:
  ```powershell
  Set-Service -Name docker -StartupType Automatic
  ```

================================================================
AŞAMA 4: DOCKER COMPOSE KURULUMU (10 dakika)
================================================================

4.1 DOCKER COMPOSE İNDİRME
---------------------------
• Docker Compose'u indirin:
  ```powershell
  # En son versiyonu indirme
  $dockerComposeVersion = "2.23.0"
  Invoke-WebRequest -Uri "https://github.com/docker/compose/releases/download/v$dockerComposeVersion/docker-compose-windows-x86_64.exe" -OutFile "$env:ProgramFiles\Docker\docker-compose.exe"
  ```

• Docker Compose versiyonunu kontrol edin:
  ```powershell
  docker-compose --version
  ```

================================================================
AŞAMA 5: DOCKER KURULUM DOĞRULAMA (5 dakika)
================================================================

5.1 DOCKER VERSİYON KONTROLÜ
-----------------------------
• Docker versiyonunu kontrol edin:
  ```powershell
  docker --version
  docker-compose --version
  ```

5.2 DOCKER DAEMON DURUMU
-------------------------
• Docker daemon'ın çalıştığını kontrol edin:
  ```powershell
  docker info
  ```

5.3 TEST CONTAINER ÇALIŞTIRMA
------------------------------
• Basit bir test container'ı çalıştırın:
  ```powershell
  docker run hello-world
  ```

• Eğer "Hello from Docker!" mesajını görürseniz kurulum başarılı!

================================================================
AŞAMA 6: DOCKER CONFIGURATION (15 dakika)
================================================================

6.1 DOCKER DAEMON CONFIGURATION
--------------------------------
• Docker daemon configuration dosyası oluşturun:
  ```powershell
  # Configuration dizini oluşturma
  New-Item -ItemType Directory -Path "C:\ProgramData\docker\config" -Force
  ```

• daemon.json dosyası oluşturun:
  ```json
  {
    "hosts": ["tcp://0.0.0.0:2376", "npipe://"],
    "log-driver": "json-file",
    "log-opts": {
      "max-size": "10m",
      "max-file": "3"
    },
    "storage-driver": "windowsfilter",
    "data-root": "C:\\ProgramData\\docker",
    "exec-opts": ["isolation=process"],
    "experimental": false
  }
  ```

6.2 FIREWALL KURALLARI
-----------------------
• Docker için firewall kuralları ekleyin:
  ```powershell
  # Docker daemon port
  New-NetFirewallRule -DisplayName "Docker Daemon" -Direction Inbound -Protocol TCP -LocalPort 2376 -Action Allow
  
  # Redis port (ileride kullanacağımız)
  New-NetFirewallRule -DisplayName "Redis Port" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow
  ```

6.3 DOCKER SERVICE YENİDEN BAŞLATMA
------------------------------------
• Configuration değişikliklerini uygulamak için:
  ```powershell
  Restart-Service docker
  ```

================================================================
AŞAMA 7: PRODUCTION OPTİMİZASYONU (10 dakika)
================================================================

7.1 DOCKER LOGGING CONFIGURATION
---------------------------------
• Log rotation ayarları:
  ```powershell
  # Docker log dosyalarının boyutunu sınırlama
  docker system prune -f
  ```

7.2 DOCKER SYSTEM MONITORING
-----------------------------
• Docker system bilgilerini kontrol edin:
  ```powershell
  docker system df
  docker system info
  ```

7.3 AUTOMATIC CLEANUP SCRIPT
-----------------------------
• Otomatik temizlik script'i oluşturun:
  ```powershell
  # C:\Scripts\docker-cleanup.ps1
  $scriptContent = @"
# Docker otomatik temizlik script'i
docker system prune -f
docker volume prune -f
docker image prune -f
Write-Host "Docker cleanup tamamlandı: `$(Get-Date)"
"@

  New-Item -ItemType Directory -Path "C:\Scripts" -Force
  $scriptContent | Out-File -FilePath "C:\Scripts\docker-cleanup.ps1" -Encoding UTF8
  ```

• Scheduled Task oluşturun:
  ```powershell
  $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File C:\Scripts\docker-cleanup.ps1"
  $trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At "02:00"
  $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
  Register-ScheduledTask -TaskName "Docker Cleanup" -Action $action -Trigger $trigger -Settings $settings -User "SYSTEM"
  ```

================================================================
AŞAMA 8: REDİS CONTAINER HAZIRLIĞI (5 dakika)
================================================================

8.1 REDIS İMAGE İNDİRME
-----------------------
• Redis image'ını önceden indirin:
  ```powershell
  docker pull redis:7-alpine
  ```

8.2 DOCKER NETWORK OLUŞTURMA
-----------------------------
• GymKod için özel network oluşturun:
  ```powershell
  docker network create gymkod-network
  ```

8.3 DOCKER VOLUME OLUŞTURMA
----------------------------
• Redis data volume'u oluşturun:
  ```powershell
  docker volume create redis_data
  ```

================================================================
AŞAMA 9: KURULUM DOĞRULAMA VE TEST (10 dakika)
================================================================

9.1 DOCKER ENGINE DURUMU
-------------------------
• Tüm Docker bileşenlerini kontrol edin:
  ```powershell
  # Service durumu
  Get-Service docker
  
  # Docker info
  docker info
  
  # Network listesi
  docker network ls
  
  # Volume listesi
  docker volume ls
  
  # Image listesi
  docker images
  ```

9.2 SYSTEM RESOURCE KONTROLÜ
-----------------------------
• System resource kullanımını kontrol edin:
  ```powershell
  # Memory kullanımı
  Get-WmiObject -Class Win32_OperatingSystem | Select-Object TotalVisibleMemorySize, FreePhysicalMemory
  
  # Disk kullanımı
  Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, Size, FreeSpace
  ```

================================================================
BAŞARILI KURULUM KONTROL LİSTESİ
================================================================

✅ KONTROL EDİLECEKLER:
- [ ] Windows Server 2019 güncellenmiş
- [ ] PowerShell 5.1+ kurulu
- [ ] Containers feature etkin
- [ ] Chocolatey kurulu
- [ ] Docker Engine kurulu ve çalışıyor
- [ ] Docker Compose kurulu
- [ ] docker --version komutu çalışıyor
- [ ] docker info komutu çalışıyor
- [ ] hello-world container'ı çalıştı
- [ ] Firewall kuralları eklendi
- [ ] Redis image indirildi
- [ ] gymkod-network oluşturuldu
- [ ] redis_data volume oluşturuldu

================================================================
SORUN GİDERME
================================================================

SORUN: Docker service başlamıyor
ÇÖZÜM: 
```powershell
# Service'i manuel başlatma
net start docker

# Service loglarını kontrol etme
Get-EventLog -LogName Application -Source Docker
```

SORUN: "docker: command not found"
ÇÖZÜM:
```powershell
# PATH'i yeniden yükleme
$env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine")

# PowerShell'i yeniden başlatma
```

SORUN: Container çalışmıyor
ÇÖZÜM:
```powershell
# Container loglarını kontrol etme
docker logs <container_name>

# System events kontrol etme
docker system events
```

================================================================
SONRAKI ADIM: REDİS KURULUMU
================================================================

Docker Engine kurulumu tamamlandıktan sonra:

1. Redis_Kurulum_Script.ps1'i çalıştırın:
   ```powershell
   .\Redis_Kurulum_Script.ps1 -SkipDockerInstall
   ```

2. Veya manuel olarak Redis container'ını başlatın:
   ```powershell
   # GymProject dizinine gidin
   cd C:\GymProject
   
   # Redis container'ını başlatın
   docker-compose -f docker-compose.prod.yml up -d
   ```

🎯 KURULUM TAMAMLANDI!
Docker Engine artık production-ready durumda ve Redis kurulumu için hazır.
